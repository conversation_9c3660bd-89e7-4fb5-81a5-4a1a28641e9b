<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Table Test Page - PageMate</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #4f46e5;
            border-bottom: 2px solid #4f46e5;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #4f46e5;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background: #f9f9f9;
        }
        tr:hover {
            background: #f0f0f0;
        }
        .form-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #374151;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        input[type="text"]:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }
        .instructions {
            background: #e0f2fe;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #0284c7;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #0284c7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PageMate Table Test Page</h1>
        
        <div class="instructions">
            <h3>📊 Testing Table Mode</h3>
            <p><strong>How to test:</strong></p>
            <ol>
                <li>Go to PageMate Settings and select "Tables" mode</li>
                <li>In Variables tab, use table column headers as source selectors</li>
                <li>Example: Use "رقم بند التجارة العالمي" as source to get "06281080011832"</li>
                <li>Use form field IDs like "#globalTradeNumber" as destination</li>
            </ol>
        </div>

        <div class="section">
            <h2>📋 Sample Data Table</h2>
            <table class="ant-table">
                <thead>
                    <tr>
                        <th class="ant-table-align-right">رقم بند التجارة العالمي</th>
                        <th>اسم الدواء</th>
                        <th>رقم الدفعة</th>
                        <th>تاريخ انتهاء الصلاحية</th>
                        <th>الكمية المطلوبة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>06281080011832</td>
                        <td>RIAZOLIN</td>
                        <td>25DB15</td>
                        <td>31-03-2028</td>
                        <td>150</td>
                    </tr>
                    <tr>
                        <td>06281080022943</td>
                        <td>PARACETAMOL</td>
                        <td>26DB16</td>
                        <td>15-06-2029</td>
                        <td>200</td>
                    </tr>
                    <tr>
                        <td>06281080033054</td>
                        <td>IBUPROFEN</td>
                        <td>27DB17</td>
                        <td>20-09-2030</td>
                        <td>75</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>📝 Destination Form</h2>
            <div class="form-section">
                <div class="form-group">
                    <label for="globalTradeNumber">رقم بند التجارة العالمي:</label>
                    <input type="text" id="globalTradeNumber" name="globalTradeNumber" placeholder="Enter global trade number">
                </div>
                
                <div class="form-group">
                    <label for="drugName">اسم الدواء:</label>
                    <input type="text" id="drugName" name="drugName" placeholder="Enter drug name">
                </div>
                
                <div class="form-group">
                    <label for="batchNumber">رقم الدفعة:</label>
                    <input type="text" id="batchNumber" name="batchNumber" placeholder="Enter batch number">
                </div>
                
                <div class="form-group">
                    <label for="expiryDate">تاريخ انتهاء الصلاحية:</label>
                    <input type="text" id="expiryDate" name="expiryDate" placeholder="DD-MM-YYYY">
                </div>
                
                <div class="form-group">
                    <label for="quantity">الكمية المطلوبة:</label>
                    <input type="text" id="quantity" name="quantity" placeholder="Enter quantity">
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🧪 Test Variables</h2>
            <div class="instructions">
                <h3>Suggested Variable Configurations:</h3>
                <ul>
                    <li><strong>Source:</strong> رقم بند التجارة العالمي → <strong>Destination:</strong> #globalTradeNumber</li>
                    <li><strong>Source:</strong> اسم الدواء → <strong>Destination:</strong> #drugName</li>
                    <li><strong>Source:</strong> رقم الدفعة → <strong>Destination:</strong> #batchNumber</li>
                    <li><strong>Source:</strong> تاريخ انتهاء الصلاحية → <strong>Destination:</strong> #expiryDate</li>
                    <li><strong>Source:</strong> الكمية المطلوبة → <strong>Destination:</strong> #quantity</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
