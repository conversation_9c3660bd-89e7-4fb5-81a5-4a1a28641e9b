<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multiple Variables Transfer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .source-page {
            background: #e8f5e8;
        }
        .destination-page {
            background: #e8f0ff;
        }
        .test-values {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .value-item {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h3>🧪 Multiple Variables Transfer Test</h3>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Open this page in two browser tabs</li>
            <li>Configure the extension with multiple variables using the selectors below</li>
            <li>Test transferring all variables at once</li>
        </ol>
        <p><strong>Test Variables (New Simplified Format):</strong></p>
        <ul>
            <li><code>#username</code> → <code>#username</code></li>
            <li><code>#email</code> → <code>#email</code></li>
            <li><code>#country</code> → <code>#country</code></li>
            <li><code>#bio</code> → <code>#bio</code></li>
        </ul>
        <p><strong>Legacy Format (Still Supported):</strong></p>
        <ul>
            <li><code>input-#username</code> → <code>#username</code></li>
            <li><code>input-#email</code> → <code>#email</code></li>
            <li><code>select-#country</code> → <code>#country</code></li>
            <li><code>textarea-#bio</code> → <code>#bio</code></li>
        </ul>
    </div>

    <div class="container source-page">
        <h2>📤 Source Page (Copy FROM here)</h2>
        <p>This simulates the source system with data to copy.</p>

        <div class="test-values">
            <div class="value-item">
                <label for="username">Username:</label>
                <input type="text" id="username" value="john_doe_2024">
            </div>

            <div class="value-item">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>">
            </div>

            <div class="value-item">
                <label for="country">Country:</label>
                <select id="country">
                    <option value="">Select Country</option>
                    <option value="US" selected>United States</option>
                    <option value="CA">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                </select>
            </div>

            <div class="value-item">
                <label for="bio">Bio:</label>
                <textarea id="bio" rows="3">Software developer with 5+ years experience in web development. Passionate about creating user-friendly applications.</textarea>
            </div>
        </div>
    </div>

    <div class="container destination-page">
        <h2>📥 Destination Page (Copy TO here)</h2>
        <p>This simulates the destination system where data should be pasted.</p>

        <div class="test-values">
            <div class="value-item">
                <label for="username">Username:</label>
                <input type="text" id="username" placeholder="Username will appear here">
            </div>

            <div class="value-item">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="Email will appear here">
            </div>

            <div class="value-item">
                <label for="country">Country:</label>
                <select id="country">
                    <option value="">Select Country</option>
                    <option value="US">United States</option>
                    <option value="CA">Canada</option>
                    <option value="UK">United Kingdom</option>
                    <option value="DE">Germany</option>
                    <option value="FR">France</option>
                </select>
            </div>

            <div class="value-item">
                <label for="bio">Bio:</label>
                <textarea id="bio" rows="3" placeholder="Bio will appear here"></textarea>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>🔧 Test Results</h3>
        <p>After running the transfer, check that all 4 values were copied correctly:</p>
        <ul>
            <li>✅ Username: john_doe_2024</li>
            <li>✅ Email: <EMAIL></li>
            <li>✅ Country: US (United States)</li>
            <li>✅ Bio: Software developer with 5+ years experience...</li>
        </ul>
        <p><strong>Expected behavior:</strong> All 4 variables should transfer in sequence, and you should see individual history entries for each variable showing "Variable 1/4", "Variable 2/4", etc.</p>
    </div>

    <script>
        // Add some visual feedback when values are updated
        document.addEventListener('input', function(e) {
            if (e.target.matches('input, select, textarea')) {
                e.target.style.backgroundColor = '#d4edda';
                setTimeout(() => {
                    e.target.style.backgroundColor = '';
                }, 1000);
            }
        });

        // Log when extension updates values
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                    console.log('Value updated:', mutation.target.id, '=', mutation.target.value);
                }
            });
        });

        // Observe all input elements
        document.querySelectorAll('input, select, textarea').forEach(element => {
            observer.observe(element, { attributes: true, attributeFilter: ['value'] });
        });
    </script>
</body>
</html>
