# Quick Start Guide - PageMate Extension

## 🚀 Install in 3 Steps

1. **Open Chrome Extensions**
   - Go to `chrome://extensions/`
   - Enable "Developer mode" (top right)

2. **Load Extension**
   - Click "Load unpacked"
   - Select this folder

3. **Start Using**
   - Click the extension icon in your toolbar
   - Ready to transfer values!

## 📝 Basic Usage

### Source Format: `elementType-selector`
- `div-#username` → div with ID "username"
- `input-.email` → input with class "email"
- `span-*title` → span with name "title"

### Destination Format: `selector`
- `#username` → element with ID "username"
- `.email` → element with class "email"
- `*title` → element with name "title"

## 🎯 Quick Examples

**Copy text from div to input:**
- Source: `div-#sourceText`
- Destination: `#targetInput`
- Click "Transfer Value"

**Copy email from span to input:**
- Source: `span-.email-display`
- Destination: `.email-input`
- Click "Transfer Value"

## 🧪 Test It Out

1. Open `test-page.html` in your browser
2. Install the extension
3. Try the examples provided on the test page
4. Watch values transfer with visual feedback!

## 💡 Pro Tips

- Extension remembers your last used selectors
- Destination elements get highlighted when updated
- Works across different tabs and pages
- Supports all input types and form elements

## 🔧 Troubleshooting

**Extension won't load?**
- Check all files are in the same folder
- Make sure Developer mode is enabled

**Can't find elements?**
- Verify selector format is correct
- Check element exists on current page
- Try refreshing the page

**Need help?**
- Check the full README.md for detailed instructions
- Look at INSTALLATION.md for complete setup guide
