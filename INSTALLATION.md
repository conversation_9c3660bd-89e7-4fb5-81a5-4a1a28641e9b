# Installation Guide - PageMate Extension

## Prerequisites
- Google Chrome, Microsoft Edge, or any Chromium-based browser
- Developer mode enabled in browser extensions

## Step-by-Step Installation

### 1. Prepare the Extension Files
Make sure you have all the required files in your extension folder:
- `manifest.json`
- `popup.html`
- `popup.css`
- `popup.js`
- `content.js`
- `background.js`

**Note**: The extension works without custom icons - Chrome will use a default icon. See ICON_GUIDE.md if you want to add custom icons later.

### 2. Install in Chrome/Edge

1. **Open Extensions Page**
   - Chrome: Navigate to `chrome://extensions/`
   - Edge: Navigate to `edge://extensions/`

2. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top-right corner

3. **Load the Extension**
   - Click "Load unpacked" button
   - Select the folder containing your extension files
   - The extension should appear in your extensions list

4. **Pin the Extension** (Optional)
   - Click the puzzle piece icon in the browser toolbar
   - Find "PageMate" and click the pin icon

### 4. Test the Installation

1. **Open the test page**
   - Open `test-page.html` in your browser
   - Or navigate to any webpage with form elements

2. **Access the extension**
   - Click the extension icon in your browser toolbar
   - The popup should open with source and destination fields

3. **Test basic functionality**
   - Try copying a value using the format: `div-#elementId` → `#targetId`
   - Check that the status messages appear correctly

## Troubleshooting

### Extension doesn't load
- Check that all required files are present
- Verify the `manifest.json` syntax is correct
- Look for errors in the Extensions page

### Icons not showing
- Extension uses Chrome's default icon (this is normal)
- To add custom icons, see ICON_GUIDE.md
- Default icon doesn't affect functionality

### Extension popup doesn't open
- Check browser console for JavaScript errors
- Verify popup.html and popup.js are error-free
- Try reloading the extension

### Content script not working
- Refresh the webpage after installing the extension
- Check that the website allows content scripts
- Look for errors in the browser console

## Permissions Explained

The extension requires these permissions:
- **activeTab**: Access the currently active tab
- **storage**: Save user preferences and settings
- **tabs**: Manage browser tabs for cross-tab functionality
- **scripting**: Inject content scripts into web pages
- **host_permissions**: Access all websites to read/modify elements

## Browser Compatibility

- ✅ Google Chrome (88+)
- ✅ Microsoft Edge (88+)
- ✅ Brave Browser
- ✅ Opera (74+)
- ❌ Firefox (requires Manifest V2 conversion)
- ❌ Safari (requires different extension format)

## Security Notes

- The extension only accesses elements you specify
- No data is sent to external servers
- All operations happen locally in your browser
- Source code is open and auditable

## Next Steps

After successful installation:
1. Read the main README.md for usage instructions
2. Try the examples on the test page
3. Test with your own web applications
4. Report any issues or feature requests
