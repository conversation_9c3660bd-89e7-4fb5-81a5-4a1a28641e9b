# URL Targeting Guide - Precise Tab Control

## 🎯 New Feature: URL-Specific Targeting

You can now specify exactly which tab to copy from and to by adding `@url` to your selectors!

## 📝 Basic Syntax

### Source Elements
- **Without URL**: `div-#gtin` (searches all tabs)
- **With URL**: `div-#<EMAIL>` (only specific tab)

### Destination Elements  
- **Without URL**: `*gtin` (searches all tabs)
- **With URL**: `*<EMAIL>` (only specific tab)

## 🌐 URL Matching

The extension uses **partial URL matching**, so you can use:
- **Domain**: `@example.com`
- **Subdomain**: `@app.example.com`
- **Path**: `@localhost:3000`
- **Keyword**: `@drug-system`

## 💡 Real Examples for Your Drug System

### Example 1: Copy GTIN from Drug Entry to Inventory
```
Source: div-#<EMAIL>
Destination: *<EMAIL>
```

### Example 2: Copy from localhost to production
```
Source: span-.drugCode@localhost
Destination: #<EMAIL>
```

### Example 3: Copy between different ports
```
Source: input-#batchNumber@localhost:3000
Destination: *batchNumber@localhost:8080
```

## 🔧 Using the Tab Helper

1. **Click "📋 Show Open Tabs"** in the extension popup
2. **See all available tabs** with their domains
3. **Copy the @domain** part for your selectors
4. **Use exact domains** for precise targeting

## ⚡ Smart Behavior

### Without @url (Original behavior):
1. Try current tab first
2. If not found, search all tabs
3. Use first match found

### With @url (New precise targeting):
1. Only search tabs matching the URL
2. Faster and more reliable
3. No accidental matches

## 📋 Step-by-Step for Your Case

**Your successful transfer was:**
- Source: `#gtin` (found on current tab)
- Destination: `*gtin` (found on different tab)

**To make it more precise:**

1. **Find your tab URLs** using the "Show Open Tabs" button
2. **Add URL targeting**:
   ```
   Source: div-#<EMAIL>
   Destination: *<EMAIL>
   ```

## 🎯 Benefits of URL Targeting

### ✅ **Precision**
- No more guessing which tab
- Exact source and destination control
- Prevents accidental transfers

### ✅ **Speed**
- Faster transfers (no searching all tabs)
- Immediate targeting
- Better performance

### ✅ **Reliability**
- Consistent results
- No conflicts between similar elements
- Works even with multiple tabs of same site

## 🔍 Troubleshooting URL Targeting

### "Source element not found on specified tab"
- Check the URL is correct using "Show Open Tabs"
- Make sure the tab is fully loaded
- Try using just the domain part (e.g., `@example.com`)

### "Destination element not found on specified tab"
- Verify the destination tab URL
- Check if the input element exists on that specific page
- Use browser console to test: `document.querySelector('input[name="gtin"]')`

## 📖 Quick Reference

| Format | Example | Description |
|--------|---------|-------------|
| `element-#id` | `div-#gtin` | Any tab with this element |
| `element-#id@url` | `div-#<EMAIL>` | Specific tab only |
| `#id` | `#gtin` | Any tab with this input |
| `#id@url` | `#<EMAIL>` | Specific tab only |
| `*name` | `*gtin` | Any tab with this input |
| `*name@url` | `*gtin@localhost` | Specific tab only |

## 🚀 Pro Tips

1. **Use partial URLs** for flexibility (e.g., `@localhost` instead of `@localhost:3000`)
2. **Test without @url first** to make sure elements exist
3. **Use "Show Open Tabs"** to see exact domains
4. **Combine with element inspection** to get perfect selectors

This makes your extension much more powerful and precise for cross-tab operations!
