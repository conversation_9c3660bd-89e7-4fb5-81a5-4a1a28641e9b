# PageMate Browser Extension

Your intelligent companion for transferring values between elements across different tabs and pages.

## Features

- **Smart Element Selection**: Support for ID, class, and name selectors
- **Cross-Element Transfer**: Copy values from any element type to input fields
- **Beautiful UI**: Modern, gradient-based interface
- **Real-time Feedback**: Visual indicators and status messages
- **Persistent Settings**: Remembers your last used selectors

## Installation

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right corner
3. Click "Load unpacked" and select the extension folder
4. The extension icon will appear in your browser toolbar

## Usage

### Source Element Format (New Simplified)
Use standard CSS selectors (same as destination):

Examples:
- `#username` - element with ID "username"
- `.email` - element with class "email"
- `*title` - element with name attribute "title"

### Legacy Source Format (Still Supported)
Use the format: `elementType-selector`

Examples:
- `div-#username` - div element with ID "username"
- `input-.email` - input element with class "email"
- `span-*title` - span element with name attribute "title"

### Destination Element Format
Use standard CSS selectors:

Examples:
- `#username` - element with ID "username"
- `.email` - element with class "email"
- `*title` - element with name attribute "title"

### Supported Selector Types
- `#` - Select by ID attribute
- `.` - Select by class name
- `*` - Select by name attribute

### How to Transfer Values

1. Click the extension icon in your browser toolbar
2. Enter the source element selector (e.g., `#sourceField` or legacy `div-#sourceField`)
3. Enter the destination element selector (e.g., `#targetField`)
4. Click "Transfer Value"
5. The value will be copied and the destination element will be highlighted

## Supported Element Types

### Source Elements
- `div`, `span`, `p` - Gets text content
- `input` - Gets value (handles checkboxes/radio buttons)
- `select` - Gets selected value
- `textarea` - Gets text content

### Destination Elements
- `input` - Sets value (handles all input types)
- `select` - Sets selected option
- `textarea` - Sets text content
- Other elements - Sets text content

## Examples

### Copy from a div to an input field
- Source: `div-#userDisplayName`
- Destination: `#usernameInput`

### Copy from a span with class to an input with name
- Source: `span-.email-display`
- Destination: `*emailField`

### Copy from an input to another input
- Source: `input-#sourceEmail`
- Destination: `#targetEmail`

## Troubleshooting

### "Source element not found"
- Check that the element exists on the current page
- Verify the selector format is correct
- Make sure the element type matches (div, input, etc.)

### "Destination element not found"
- Ensure the target element exists on the current page
- Check the selector syntax (#, ., or *)
- Verify the element can accept the value type

### "Could not connect to page"
- Refresh the page and try again
- Make sure the extension has permission to access the site
- Check that the page has finished loading

## Technical Details

- **Manifest Version**: 3
- **Permissions**: activeTab, storage, tabs, scripting
- **Content Scripts**: Automatically injected on all pages
- **Cross-tab Support**: Planned for future versions

## Future Enhancements

- Cross-tab value transfer
- Bulk transfer operations
- Custom element highlighting
- Export/import selector presets
- Advanced selector patterns

## Support

If you encounter any issues or have feature requests, please check the console for error messages and ensure all selectors are properly formatted.
