// Content script for PageMate extension
(function () {
    'use strict';

    let isHuntingMode = false;
    let huntingOverlay = null;
    let currentHighlightedElement = null;

    // Auto trigger variables
    let autoTriggerEnabled = false;
    let triggerType = 'manual';
    let buttonSelector = '';
    let elementSelector = '';
    let textObserver = null;
    let monitoredElements = new Map(); // Store original text values

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        try {
            if (request.action === 'getValue') {
                const transferMode = request.transferMode || 'default';
                const value = getElementValue(request.selector, transferMode);
                sendResponse({ success: true, value: value });
            } else if (request.action === 'setValue') {
                // Handle both synchronous and asynchronous setValue operations
                const result = setElementValue(request.selector, request.value);

                if (result && typeof result.then === 'function') {
                    // It's a Promise (Ant DatePicker case)
                    result
                        .then((success) => {
                            sendResponse({ success: success });
                        })
                        .catch((error) => {
                            console.error('Async setValue error:', error);
                            sendResponse({ success: false, error: error.message });
                        });
                    return true; // Keep message channel open for async response
                } else {
                    // It's a synchronous result
                    sendResponse({ success: result });
                }
            } else if (request.action === 'startElementHunting') {
                startElementHunting();
                sendResponse({ success: true });
            } else if (request.action === 'stopElementHunting') {
                stopElementHunting();
                sendResponse({ success: true });
            } else if (request.action === 'setupAutoTrigger') {
                setupAutoTrigger(request.settings);
                sendResponse({ success: true });
            } else if (request.action === 'stopAutoTrigger') {
                stopAutoTrigger();
                sendResponse({ success: true });
            } else if (request.action === 'showToast') {
                showToast(request.message, request.type);
                sendResponse({ success: true });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ success: false, error: error.message });
        }

        return true; // Keep message channel open for async response
    });

    function getElementValue(selector, transferMode = 'default') {
        if (transferMode === 'tables') {
            return getTableValue(selector);
        } else {
            return getDefaultElementValue(selector);
        }
    }

    function getDefaultElementValue(selector) {
        const { elementType, selectorType, selectorValue } = selector;

        // Build the CSS selector
        let cssSelector;
        if (elementType) {
            // Legacy format with specific element type
            switch (selectorType) {
                case '#':
                    cssSelector = `${elementType}#${selectorValue}`;
                    break;
                case '.':
                    cssSelector = `${elementType}.${selectorValue}`;
                    break;
                case '*':
                    cssSelector = `${elementType}[name="${selectorValue}"]`;
                    break;
                default:
                    throw new Error(`Invalid selector type: ${selectorType}`);
            }
        } else {
            // New simplified format - auto-detect element type
            switch (selectorType) {
                case '#':
                    cssSelector = `#${selectorValue}`;
                    break;
                case '.':
                    cssSelector = `.${selectorValue}`;
                    break;
                case '*':
                    cssSelector = `[name="${selectorValue}"]`;
                    break;
                default:
                    throw new Error(`Invalid selector type: ${selectorType}`);
            }
        }

        const element = document.querySelector(cssSelector);
        if (!element) {
            throw new Error(`Source element not found: ${cssSelector}`);
        }

        // Get value based on element type
        let value = null;

        if (element.tagName.toLowerCase() === 'input') {
            const inputType = element.type.toLowerCase();
            if (inputType === 'checkbox' || inputType === 'radio') {
                value = element.checked ? element.value : '';
            } else {
                value = element.value;
            }
        } else if (element.tagName.toLowerCase() === 'select') {
            value = element.value;
        } else if (element.tagName.toLowerCase() === 'textarea') {
            value = element.value;
        } else {
            // For other elements, try to get text content or data attributes
            value = element.textContent || element.innerText || element.getAttribute('data-value') || '';
        }

        console.log(`Retrieved value "${value}" from element:`, element);
        return value;
    }

    function getTableValue(selector) {
        // In table mode, the selector contains the column header text
        const headerText = selector.selectorValue || selector;

        console.log(`Looking for table column with header: "${headerText}"`);

        // Find all tables on the page
        const tables = document.querySelectorAll('table');

        for (const table of tables) {
            try {
                // Find header cell that matches the text
                const headerCells = table.querySelectorAll('th, thead td');
                let columnIndex = -1;

                for (let i = 0; i < headerCells.length; i++) {
                    const cellText = (headerCells[i].textContent || headerCells[i].innerText || '').trim();
                    if (cellText === headerText) {
                        columnIndex = i;
                        break;
                    }
                }

                if (columnIndex === -1) {
                    continue; // Try next table
                }

                console.log(`Found column "${headerText}" at index ${columnIndex}`);

                // Find the first data row (skip header rows)
                const tbody = table.querySelector('tbody');
                const dataRows = tbody ? tbody.querySelectorAll('tr') : table.querySelectorAll('tr:not(:first-child)');

                if (dataRows.length === 0) {
                    continue; // Try next table
                }

                // Get the first data row
                const firstDataRow = dataRows[0];
                const dataCells = firstDataRow.querySelectorAll('td, th');

                if (columnIndex < dataCells.length) {
                    const cellValue = (dataCells[columnIndex].textContent || dataCells[columnIndex].innerText || '').trim();
                    console.log(`Retrieved table value "${cellValue}" from column "${headerText}"`);
                    return cellValue;
                }

            } catch (error) {
                console.log(`Error processing table:`, error);
                continue; // Try next table
            }
        }

        throw new Error(`Table column with header "${headerText}" not found`);
    }

    function setElementValue(selector, value) {
        const { selectorType, selectorValue } = selector;

        // Build the CSS selector for destination - prioritize input elements
        let cssSelector;
        switch (selectorType) {
            case '#':
                cssSelector = `input#${selectorValue}`;
                break;
            case '.':
                cssSelector = `input.${selectorValue}`;
                break;
            case '*':
                cssSelector = `input[name="${selectorValue}"]`;
                break;
            default:
                throw new Error(`Invalid selector type: ${selectorType}`);
        }

        let element = document.querySelector(cssSelector);

        // If input-specific selector fails, try general selector as fallback
        if (!element) {
            switch (selectorType) {
                case '#':
                    cssSelector = `#${selectorValue}`;
                    break;
                case '.':
                    cssSelector = `.${selectorValue}`;
                    break;
                case '*':
                    cssSelector = `[name="${selectorValue}"]`;
                    break;
            }
            element = document.querySelector(cssSelector);
        }

        if (!element) {
            // Log available input elements for debugging
            console.log('Available input elements on page:', {
                byId: Array.from(document.querySelectorAll('input[id]')).map(el => `#${el.id}`),
                byClass: Array.from(document.querySelectorAll('input[class]')).map(el => `.${el.className.split(' ').join('.')}`),
                byName: Array.from(document.querySelectorAll('input[name]')).map(el => `*${el.name}`)
            });
            throw new Error(`Destination input element not found: ${cssSelector}`);
        }

        // Check if this is an Ant Design date picker
        if (window.PageMateAntDatePicker && window.PageMateAntDatePicker.isAntDatePicker(element)) {
            console.log('🗓️ Detected Ant Design date picker, using custom handler...');

            return window.PageMateAntDatePicker.setDateValue(element, value)
                .then(() => {
                    console.log('✅ Ant DatePicker value set successfully');
                    highlightElementForTransfer(element);
                    return true;
                })
                .catch((error) => {
                    console.error('❌ Ant DatePicker failed, falling back to standard method:', error);
                    // Fall back to standard method
                    return setElementValueStandard(element, value);
                });
        }

        // Use standard method for non-Ant Design elements
        return setElementValueStandard(element, value);
    }

    function setElementValueStandard(element, value) {
        // Verify it's an input element or compatible element
        const tagName = element.tagName.toLowerCase();

        if (tagName === 'input') {
            const inputType = element.type.toLowerCase();
            if (inputType === 'checkbox' || inputType === 'radio') {
                element.checked = Boolean(value);
            } else {
                element.value = value;
            }

            // Trigger input events to notify any listeners
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));

        } else if (tagName === 'select') {
            element.value = value;
            element.dispatchEvent(new Event('change', { bubbles: true }));

        } else if (tagName === 'textarea') {
            element.value = value;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));

        } else {
            // Warn if not an input element but still try to set value
            console.warn(`Warning: Destination element is not an input element (${tagName}), but attempting to set value anyway`);
            if (element.hasAttribute('contenteditable')) {
                element.textContent = value;
            } else {
                element.value = value;
            }
        }

        console.log(`Set value "${value}" to ${tagName} element:`, element);

        // Highlight the element briefly to show it was updated
        highlightElementForTransfer(element);

        return true;
    }

    function highlightElementForTransfer(element) {
        const originalStyle = element.style.cssText;
        element.style.cssText += '; border: 2px solid #4CAF50 !important; background-color: #E8F5E8 !important; transition: all 0.3s ease !important;';

        setTimeout(() => {
            element.style.cssText = originalStyle;
        }, 1500);
    }

    // Add visual indicator that the extension is active
    function addExtensionIndicator() {
        if (document.getElementById('evt-indicator')) return;

        const indicator = document.createElement('div');
        indicator.id = 'evt-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 11px;
            font-family: Arial, sans-serif;
            z-index: 10000;
            opacity: 0.8;
            pointer-events: none;
            transition: opacity 0.3s ease;
        `;
        indicator.textContent = 'EVT Active';

        document.body.appendChild(indicator);

        // Fade out after 3 seconds
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                if (indicator.parentNode) {
                    indicator.parentNode.removeChild(indicator);
                }
            }, 300);
        }, 3000);
    }

    // Element hunting functions
    function startElementHunting() {
        if (isHuntingMode) return;

        isHuntingMode = true;
        document.body.style.cursor = 'crosshair';

        // Create hunting overlay
        createHuntingOverlay();

        // Add event listeners
        document.addEventListener('mouseover', handleElementHover, true);
        document.addEventListener('mouseout', handleElementMouseOut, true);
        document.addEventListener('click', handleElementClick, true);
        document.addEventListener('keydown', handleEscapeKey, true);

        console.log('Element hunting mode activated');
    }

    function stopElementHunting() {
        if (!isHuntingMode) return;

        isHuntingMode = false;
        document.body.style.cursor = '';

        // Remove hunting overlay
        removeHuntingOverlay();

        // Remove event listeners
        document.removeEventListener('mouseover', handleElementHover, true);
        document.removeEventListener('mouseout', handleElementMouseOut, true);
        document.removeEventListener('click', handleElementClick, true);
        document.removeEventListener('keydown', handleEscapeKey, true);

        // Clear any highlighted element
        clearElementHighlight();

        console.log('Element hunting mode deactivated');
    }

    function createHuntingOverlay() {
        if (huntingOverlay) return;

        huntingOverlay = document.createElement('div');
        huntingOverlay.id = 'pagemate-hunting-overlay';
        huntingOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.15);
            z-index: 999999;
            pointer-events: none;
        `;

        const indicator = document.createElement('div');
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-weight: 600;
            z-index: 1000000;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: gentlePulse 3s infinite ease-in-out;
            display: flex;
            align-items: center;
            gap: 8px;
        `;
        indicator.innerHTML = '<span style="opacity: 0.8; font-weight: 400;">Click any element to copy its ID</span>';

        // Add gentle pulse animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes gentlePulse {
                0%, 100% {
                    transform: translateX(-50%) scale(1);
                    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.25);
                }
                50% {
                    transform: translateX(-50%) scale(1.02);
                    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.35);
                }
            }
        `;
        document.head.appendChild(style);

        huntingOverlay.appendChild(indicator);
        document.body.appendChild(huntingOverlay);
    }

    function removeHuntingOverlay() {
        if (huntingOverlay && huntingOverlay.parentNode) {
            huntingOverlay.parentNode.removeChild(huntingOverlay);
            huntingOverlay = null;
        }
    }

    function handleElementHover(event) {
        if (!isHuntingMode) return;

        event.preventDefault();
        event.stopPropagation();

        const element = event.target;
        if (element === huntingOverlay || huntingOverlay?.contains(element)) return;

        highlightElement(element, true);
        currentHighlightedElement = element;
    }

    function handleElementMouseOut(event) {
        if (!isHuntingMode) return;

        const element = event.target;
        if (element === currentHighlightedElement) {
            clearElementHighlight();
            currentHighlightedElement = null;
        }
    }

    function handleElementClick(event) {
        if (!isHuntingMode) return;

        event.preventDefault();
        event.stopPropagation();

        const element = event.target;
        if (element === huntingOverlay || huntingOverlay?.contains(element)) return;

        console.log('Element clicked for hunting:', element);

        // Get element ID
        const elementId = element.id;

        if (elementId) {
            // Copy ID to clipboard with # prefix
            const selectorText = `#${elementId}`;

            // Use the Clipboard API
            navigator.clipboard.writeText(selectorText).then(() => {
                console.log('Element ID copied to clipboard:', selectorText);
                showSuccessMessage(`✅ Copied: ${selectorText}`);
            }).catch(err => {
                console.error('Failed to copy to clipboard:', err);
                showErrorMessage('❌ Failed to copy ID to clipboard');
            });
        } else {
            console.log('Element has no ID');
            showErrorMessage('⚠️ Element has no ID attribute');
        }

        // Stop hunting mode
        stopElementHunting();
    }

    function handleEscapeKey(event) {
        if (event.key === 'Escape' && isHuntingMode) {
            stopElementHunting();
            chrome.runtime.sendMessage({
                action: 'huntingCancelled'
            });
        }
    }

    function showSuccessMessage(message) {
        showToastMessage(message, 'success');
    }

    function showErrorMessage(message) {
        showToastMessage(message, 'error');
    }

    let currentToast = null;

    function showToast(message, type = 'info') {
        showToastMessage(message, type);
    }

    function showToastMessage(message, type = 'info') {
        // Remove any existing toasts first (multiple methods to ensure cleanup)
        if (currentToast && currentToast.parentNode) {
            currentToast.parentNode.removeChild(currentToast);
            currentToast = null;
        }

        // Also remove any toasts by class name (backup cleanup)
        const existingToasts = document.querySelectorAll('.pagemate-toast');
        existingToasts.forEach(toast => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });

        // Create toast element
        const toast = document.createElement('div');
        toast.className = 'pagemate-toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideIn 0.3s ease-out;
        `;

        toast.textContent = message;
        document.body.appendChild(toast);
        currentToast = toast;

        // Add animation styles if not already added
        if (!document.getElementById('pagemate-toast-styles')) {
            const style = document.createElement('style');
            style.id = 'pagemate-toast-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        // Remove toast after 3 seconds
        setTimeout(() => {
            if (toast === currentToast && toast.parentNode) {
                toast.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                    if (toast === currentToast) {
                        currentToast = null;
                    }
                }, 300);
            }
        }, 3000);
    }



    function clearElementHighlight() {
        if (currentHighlightedElement) {
            currentHighlightedElement.style.outline = '';
            currentHighlightedElement.style.backgroundColor = '';
            currentHighlightedElement.style.boxShadow = '';
        }
    }

    function highlightElement(element, isHover = false) {
        // Clear previous highlight
        clearElementHighlight();

        if (isHover) {
            // Softer blue highlight for hunting mode
            element.style.outline = '2px solid #3b82f6';
            element.style.backgroundColor = 'rgba(59, 130, 246, 0.08)';
            element.style.boxShadow = '0 0 0 1px rgba(59, 130, 246, 0.2)';
        } else {
            // Regular highlight for transfers
            element.style.outline = '3px solid #10b981';
            element.style.backgroundColor = 'rgba(16, 185, 129, 0.1)';

            setTimeout(() => {
                element.style.outline = '';
                element.style.backgroundColor = '';
            }, 2000);
        }
    }

    // Auto Trigger Functions
    function setupAutoTrigger(settings) {
        console.log('Setting up auto trigger:', settings);

        // Stop any existing triggers
        stopAutoTrigger();

        // Update settings
        autoTriggerEnabled = settings.enabled;
        triggerType = settings.triggerType;
        buttonSelector = settings.buttonSelector;
        elementSelector = settings.elementSelector;

        if (!autoTriggerEnabled || triggerType === 'manual') {
            return;
        }

        if (triggerType === 'button' && buttonSelector) {
            setupButtonTrigger();
        } else if (triggerType === 'element' && elementSelector) {
            setupElementTrigger();
        }
    }

    function stopAutoTrigger() {
        console.log('Stopping auto trigger');

        // Remove button click listeners
        if (triggerType === 'button' && buttonSelector) {
            const elements = findElementsBySelector(buttonSelector);
            elements.forEach(element => {
                element.removeEventListener('click', handleButtonTrigger);
            });
        }

        // Stop text observer
        if (textObserver) {
            textObserver.disconnect();
            textObserver = null;
        }

        // Clear monitored elements
        monitoredElements.clear();

        autoTriggerEnabled = false;
    }

    function setupButtonTrigger() {
        console.log('Setting up button trigger for:', buttonSelector);

        const elements = findElementsBySelector(buttonSelector);
        if (elements.length === 0) {
            console.warn('No button elements found for selector:', buttonSelector);
            return;
        }

        elements.forEach(element => {
            element.addEventListener('click', handleButtonTrigger);
            console.log('Added click listener to button:', element);
        });
    }

    function setupElementTrigger() {
        console.log('Setting up element trigger for:', elementSelector);

        const elements = findElementsBySelector(elementSelector);
        if (elements.length === 0) {
            console.warn('No elements found for selector:', elementSelector);
            return;
        }

        // Store initial values (text content or input value)
        elements.forEach(element => {
            let initialValue;
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                initialValue = element.value || '';
            } else {
                initialValue = (element.textContent || element.innerText || '').trim();
            }
            monitoredElements.set(element, initialValue);
            console.log('Monitoring element:', element.tagName, element, 'Initial value:', initialValue);
        });

        // Set up mutation observer to watch for text changes
        textObserver = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    checkTextChanges();
                }
            });
        });

        // Observe all monitored elements for text changes
        elements.forEach(element => {
            textObserver.observe(element, {
                childList: true,
                subtree: true,
                characterData: true
            });

            // For input elements, also listen for input events
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                element.addEventListener('input', () => {
                    console.log('Input event detected on:', element);
                    checkTextChanges();
                });
                element.addEventListener('change', () => {
                    console.log('Change event detected on:', element);
                    checkTextChanges();
                });
            }
        });
    }

    function findElementsBySelector(selector) {
        const trimmedSelector = selector.trim();

        // Parse selector with prefix support
        if (trimmedSelector.includes(':')) {
            // Handle inner text selector (e.g., "button:Save", "div:موافق / OK")
            const [tagPrefix, innerText] = trimmedSelector.split(':', 2);
            const cleanText = innerText.trim();

            if (tagPrefix) {
                // Find elements by tag and inner text
                const elements = Array.from(document.querySelectorAll(tagPrefix));
                return elements.filter(el => {
                    const elementText = (el.textContent || el.innerText || '').trim();
                    return elementText === cleanText;
                });
            } else {
                // Find any element with matching inner text
                const allElements = Array.from(document.querySelectorAll('*'));
                return allElements.filter(el => {
                    const elementText = (el.textContent || el.innerText || '').trim();
                    return elementText === cleanText;
                });
            }
        } else {
            // Handle standard selectors with prefixes
            let cssSelector = trimmedSelector;

            // Check for tag prefix (e.g., "button#save", "a.link")
            const tagPrefixMatch = trimmedSelector.match(/^([a-zA-Z]+)([#.*])(.+)$/);
            if (tagPrefixMatch) {
                const [, tag, selectorType, value] = tagPrefixMatch;
                switch (selectorType) {
                    case '#':
                        cssSelector = `${tag}#${value}`;
                        break;
                    case '.':
                        cssSelector = `${tag}.${value}`;
                        break;
                    case '*':
                        cssSelector = `${tag}[name="${value}"]`;
                        break;
                }
            } else {
                // Handle simple selectors (#id, .class, *name)
                if (trimmedSelector.startsWith('#')) {
                    cssSelector = trimmedSelector;
                } else if (trimmedSelector.startsWith('.')) {
                    cssSelector = trimmedSelector;
                } else if (trimmedSelector.startsWith('*')) {
                    cssSelector = `[name="${trimmedSelector.substring(1)}"]`;
                }
            }

            try {
                return Array.from(document.querySelectorAll(cssSelector));
            } catch (error) {
                console.error('Invalid CSS selector:', cssSelector, error);
                return [];
            }
        }
    }

    function handleButtonTrigger(event) {
        console.log('Button trigger activated:', event.target);
        triggerAutoTransfer();
    }

    function checkTextChanges() {
        monitoredElements.forEach((originalValue, element) => {
            let currentValue;
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA' || element.tagName === 'SELECT') {
                currentValue = element.value || '';
            } else {
                currentValue = (element.textContent || element.innerText || '').trim();
            }

            if (currentValue !== originalValue) {
                console.log('Value change detected:', element.tagName, element, 'Old:', originalValue, 'New:', currentValue);
                monitoredElements.set(element, currentValue); // Update stored value
                triggerAutoTransfer();
            }
        });
    }

    function triggerAutoTransfer() {
        console.log('Triggering auto transfer');

        // Send message to background script to trigger transfer
        chrome.runtime.sendMessage({
            action: 'triggerAutoTransfer'
        }, (response) => {
            if (chrome.runtime.lastError) {
                console.error('Error triggering auto transfer:', chrome.runtime.lastError);
            } else {
                console.log('Auto transfer triggered successfully');
            }
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addExtensionIndicator);
    } else {
        addExtensionIndicator();
    }

})();
