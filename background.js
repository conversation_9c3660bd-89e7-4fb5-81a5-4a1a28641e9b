// Background script for PageMate extension
chrome.runtime.onInstalled.addListener(() => {
    console.log('PageMate extension installed');
});

// Listen for storage changes to handle auto transfer state changes
chrome.storage.onChanged.addListener((changes, namespace) => {
    if (namespace === 'local' && changes.autoTransferEnabled) {
        const newValue = changes.autoTransferEnabled.newValue;
        const oldValue = changes.autoTransferEnabled.oldValue;

        console.log('Auto transfer state changed:', oldValue, '->', newValue);

        if (newValue && !oldValue) {
            // Auto transfer was enabled
            console.log('Setting up auto triggers on all tabs');
            setupAutoTriggersOnAllTabs();
        } else if (!newValue && oldValue) {
            // Auto transfer was disabled
            console.log('Stopping auto triggers on all tabs');
            stopAutoTriggersOnAllTabs();
        }
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    // This will open the popup automatically due to default_popup in manifest
});

// Listen for tab updates to inject content script if needed
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'complete' && tab.url && !tab.url.startsWith('chrome://')) {
        // Inject content scripts if not already injected
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ['ant-datepicker.js', 'content.js']
        }).then(() => {
            // After injecting content script, set up auto triggers if enabled
            setupAutoTriggersForTab(tabId);
        }).catch(() => {
            // Ignore errors (script might already be injected)
            // Still try to set up auto triggers
            setupAutoTriggersForTab(tabId);
        });
    }
});

// Function to set up auto triggers for a specific tab
async function setupAutoTriggersForTab(tabId) {
    try {
        // Get auto transfer settings from storage
        const storage = await chrome.storage.local.get([
            'autoTransferEnabled',
            'transferTrigger',
            'buttonSelector',
            'elementSelector'
        ]);

        const autoTransferEnabled = storage.autoTransferEnabled || false;
        const triggerType = storage.transferTrigger || 'manual';
        const buttonSelector = (storage.buttonSelector || '').trim();
        const elementSelector = (storage.elementSelector || '').trim();

        // Only set up if auto transfer is enabled and not manual mode
        if (!autoTransferEnabled || triggerType === 'manual') {
            return;
        }

        // Validate selectors
        if (triggerType === 'button' && !buttonSelector) {
            return;
        }
        if (triggerType === 'element' && !elementSelector) {
            return;
        }

        const settings = {
            enabled: autoTransferEnabled,
            triggerType: triggerType,
            buttonSelector: buttonSelector,
            elementSelector: elementSelector
        };

        // Send setup message to content script
        chrome.tabs.sendMessage(tabId, {
            action: 'setupAutoTrigger',
            settings: settings
        }, (response) => {
            if (chrome.runtime.lastError) {
                // Silently ignore - content script might not be ready yet
                console.log(`Could not setup auto trigger on tab ${tabId}: ${chrome.runtime.lastError.message}`);
            } else {
                console.log(`Auto trigger setup successful on tab ${tabId}`);
            }
        });

    } catch (error) {
        console.error('Error setting up auto triggers for tab:', error);
    }
}

// Function to setup auto triggers on all tabs
async function setupAutoTriggersOnAllTabs() {
    try {
        // Get auto transfer settings from storage
        const storage = await chrome.storage.local.get([
            'transferTrigger',
            'buttonSelector',
            'elementSelector',
            'selectedSourceTab'
        ]);

        const triggerType = storage.transferTrigger || 'manual';
        const buttonSelector = (storage.buttonSelector || '').trim();
        const elementSelector = (storage.elementSelector || '').trim();
        const selectedSourceTab = storage.selectedSourceTab;

        // Only set up if not manual mode
        if (triggerType === 'manual') {
            console.log('Manual mode - no auto triggers to setup');
            return;
        }

        // Validate selectors
        if (triggerType === 'button' && !buttonSelector) {
            console.log('Button trigger mode but no button selector configured');
            return;
        }
        if (triggerType === 'element' && !elementSelector) {
            console.log('Element trigger mode but no element selector configured');
            return;
        }

        const settings = {
            enabled: true,
            triggerType: triggerType,
            buttonSelector: buttonSelector,
            elementSelector: elementSelector
        };

        // Get all tabs
        const allTabs = await chrome.tabs.query({});

        // If a specific source tab is selected, only setup on that tab
        if (selectedSourceTab) {
            console.log('Setting up auto trigger on selected source tab:', selectedSourceTab.title);

            // Find the tab by ID or title
            let targetTab = allTabs.find(tab => tab.id === selectedSourceTab.id);
            if (!targetTab) {
                targetTab = allTabs.find(tab => tab.title === selectedSourceTab.title);
            }

            if (targetTab) {
                await setupAutoTriggersForTab(targetTab.id);
            } else {
                console.log('Selected source tab not found');
            }
        } else {
            // Setup on current active tab only
            const activeTabs = await chrome.tabs.query({ active: true, currentWindow: true });
            if (activeTabs[0] && !activeTabs[0].url.startsWith('chrome://') && !activeTabs[0].url.startsWith('chrome-extension://')) {
                console.log('Setting up auto trigger on current active tab:', activeTabs[0].title);
                await setupAutoTriggersForTab(activeTabs[0].id);
            }
        }

    } catch (error) {
        console.error('Error setting up auto triggers on all tabs:', error);
    }
}

// Function to stop auto triggers on all tabs
async function stopAutoTriggersOnAllTabs() {
    try {
        const allTabs = await chrome.tabs.query({});

        for (const tab of allTabs) {
            if (!tab.url.startsWith('chrome://') && !tab.url.startsWith('chrome-extension://')) {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'stopAutoTrigger'
                }, () => {
                    // Ignore errors - tab might not have content script
                    if (chrome.runtime.lastError) {
                        // Silently ignore
                    }
                });
            }
        }

        console.log('Sent stop auto trigger messages to all tabs');
    } catch (error) {
        console.error('Error stopping auto triggers on all tabs:', error);
    }
}

// Handle cross-tab communication if needed in the future
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'crossTabTransfer') {
        // Future implementation for cross-tab transfers
        // This would involve storing data temporarily and transferring between tabs
        handleCrossTabTransfer(request, sender, sendResponse);
        return true;
    } else if (request.action === 'triggerAutoTransfer') {
        // Handle auto transfer trigger from content script
        handleAutoTransferTrigger(sender.tab.id, sendResponse);
        return true;
    }
});

function handleCrossTabTransfer(request, sender, sendResponse) {
    // Future implementation for transferring data between different tabs
    // For now, we'll focus on same-tab transfers
    sendResponse({ success: false, error: 'Cross-tab transfer not yet implemented' });
}

function handleAutoTransferTrigger(tabId, sendResponse) {
    console.log('Auto transfer triggered from tab:', tabId);

    // Execute transfer directly in background script
    executeAutoTransfer(tabId).then((result) => {
        console.log('Auto transfer completed:', result);

        // Try to notify popup if it's open
        chrome.runtime.sendMessage({
            action: 'autoTransferComplete',
            result: result
        }).catch(() => {
            // Popup might not be open, that's okay
            console.log('Popup not available for notification');
        });

        sendResponse({ success: true, result: result });
    }).catch((error) => {
        console.error('Auto transfer failed:', error);

        // Try to notify popup of error if it's open
        chrome.runtime.sendMessage({
            action: 'autoTransferComplete',
            result: { successful: 0, failed: 1, total: 1, error: error.message }
        }).catch(() => {
            // Popup might not be open, that's okay
            console.log('Popup not available for error notification');
        });

        sendResponse({ success: false, error: error.message });
    });
}

async function executeAutoTransfer(sourceTabId) {
    console.log('Executing auto transfer in background script');

    // Get variables and settings from storage
    const storage = await chrome.storage.local.get([
        'variables',
        'selectedSourceTab',
        'selectedDestinationTab'
    ]);

    const variables = storage.variables || [];
    const selectedSourceTab = storage.selectedSourceTab;
    const selectedDestinationTab = storage.selectedDestinationTab;

    // Validate variables
    const validVariables = variables.filter(v => v.source && v.destination);

    if (validVariables.length === 0) {
        throw new Error('No valid variables configured');
    }

    console.log(`Processing ${validVariables.length} variables`);

    let successfulTransfers = 0;
    let failedTransfers = 0;

    // Process each variable
    for (let i = 0; i < validVariables.length; i++) {
        const variable = validVariables[i];
        const source = variable.source.trim();
        const destination = variable.destination.trim();

        try {
            console.log(`Processing variable ${i + 1}: ${source} → ${destination}`);

            // Parse selectors
            const sourceSelector = parseSelector(source);
            const destSelector = parseSelector(destination);

            // Get source value
            let sourceValue = null;

            if (selectedSourceTab) {
                // Use selected source tab
                try {
                    await chrome.tabs.get(selectedSourceTab.id); // Check if tab still exists
                    sourceValue = await getSourceValue(selectedSourceTab.id, sourceSelector);
                } catch (e) {
                    // Tab might have been closed, try to find by title
                    const allTabs = await chrome.tabs.query({});
                    let foundTab = allTabs.find(tab => tab.title === selectedSourceTab.title);
                    if (foundTab) {
                        sourceValue = await getSourceValue(foundTab.id, sourceSelector);
                    } else {
                        throw new Error(`Source tab "${selectedSourceTab.title}" not found`);
                    }
                }
            } else {
                // Try source tab first, then search all tabs
                try {
                    sourceValue = await getSourceValue(sourceTabId, sourceSelector);
                } catch (error) {
                    // Search all tabs
                    const allTabs = await chrome.tabs.query({});
                    let found = false;

                    for (const tab of allTabs) {
                        if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                            continue;
                        }

                        try {
                            console.log(`Trying to get source value from tab: "${tab.title}"`);
                            sourceValue = await getSourceValue(tab.id, sourceSelector);
                            console.log(`Successfully got source value from tab: "${tab.title}"`);
                            found = true;
                            break;
                        } catch (e) {
                            console.log(`Failed to get source value from tab "${tab.title}":`, e.message);
                            // Continue searching
                        }
                    }

                    if (!found) {
                        throw new Error(`Source element not found: ${source}`);
                    }
                }
            }

            if (!sourceValue) {
                throw new Error(`No value found for source: ${source}`);
            }

            console.log(`Got source value: "${sourceValue}"`);

            // Set destination value
            let destinationSet = false;
            let actualDestinationTab = null;

            if (selectedDestinationTab) {
                // Use selected destination tab
                try {
                    await chrome.tabs.get(selectedDestinationTab.id); // Check if tab still exists
                    await setDestinationValue(selectedDestinationTab.id, destSelector, sourceValue);
                    actualDestinationTab = selectedDestinationTab;
                    destinationSet = true;
                } catch (e) {
                    // Tab might have been closed, try to find by title
                    const allTabs = await chrome.tabs.query({});
                    let foundTab = allTabs.find(tab => tab.title === selectedDestinationTab.title);
                    if (foundTab) {
                        await setDestinationValue(foundTab.id, destSelector, sourceValue);
                        actualDestinationTab = { id: foundTab.id, title: foundTab.title };
                        destinationSet = true;
                    } else {
                        throw new Error(`Destination tab "${selectedDestinationTab.title}" not found`);
                    }
                }
            } else {
                // Search all tabs for destination
                const allTabs = await chrome.tabs.query({});

                for (const tab of allTabs) {
                    if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://')) {
                        continue;
                    }

                    try {
                        console.log(`Trying to set destination value on tab: "${tab.title}"`);
                        await setDestinationValue(tab.id, destSelector, sourceValue);
                        console.log(`Successfully set destination value on tab: "${tab.title}"`);
                        actualDestinationTab = { id: tab.id, title: tab.title };
                        destinationSet = true;
                        break;
                    } catch (e) {
                        console.log(`Failed to set destination value on tab "${tab.title}":`, e.message);
                        // Continue searching
                    }
                }
            }

            if (!destinationSet) {
                throw new Error(`Destination element not found: ${destination}`);
            }

            console.log(`Successfully transferred: "${sourceValue}" from ${source} to ${destination}`);
            successfulTransfers++;

            // Save to history with tab information
            await saveToHistory(source, destination, sourceValue, true, null, sourceTabId, actualDestinationTab);

        } catch (error) {
            console.error(`Transfer error for variable ${i + 1}:`, error);
            failedTransfers++;

            // Save failed transfer to history
            await saveToHistory(source, destination, '', false, error.message, sourceTabId, null);
        }
    }

    const result = {
        successful: successfulTransfers,
        failed: failedTransfers,
        total: validVariables.length
    };

    console.log('Transfer summary:', result);

    // Send toast notification to source tab
    try {
        chrome.tabs.sendMessage(sourceTabId, {
            action: 'showToast',
            message: `Auto transfer completed: ${successfulTransfers} successful, ${failedTransfers} failed`,
            type: successfulTransfers > 0 && failedTransfers === 0 ? 'success' :
                  successfulTransfers > 0 ? 'warning' : 'error'
        });
    } catch (error) {
        console.log('Could not send toast notification to source tab:', error);
    }

    return result;
}

async function saveToHistory(source, destination, value, success, error = null, sourceTabId = null, destinationTab = null) {
    try {
        // Get existing history
        const storage = await chrome.storage.local.get(['transferHistory']);
        const history = storage.transferHistory || [];

        // Get tab information
        let sourceTabTitle = 'Unknown';
        let destinationTabTitle = 'Unknown';

        try {
            if (sourceTabId) {
                const sourceTab = await chrome.tabs.get(sourceTabId);
                sourceTabTitle = sourceTab.title || 'Unknown';
            }
        } catch (e) {
            console.log('Could not get source tab info:', e);
        }

        try {
            if (destinationTab && destinationTab.id) {
                const destTab = await chrome.tabs.get(destinationTab.id);
                destinationTabTitle = destTab.title || destinationTab.title || 'Unknown';
            } else if (destinationTab && destinationTab.title) {
                destinationTabTitle = destinationTab.title;
            }
        } catch (e) {
            console.log('Could not get destination tab info:', e);
        }

        // Create history entry
        const entry = {
            id: Date.now() + Math.random(),
            timestamp: new Date().toISOString(),
            source: source,
            destination: destination,
            value: value,
            success: success,
            error: error,
            type: 'auto', // Mark as auto transfer
            sourceTab: sourceTabTitle,
            destinationTab: destinationTabTitle
        };

        // Add to beginning of history
        history.unshift(entry);

        // Keep only last 100 entries
        if (history.length > 100) {
            history.splice(100);
        }

        // Save back to storage
        await chrome.storage.local.set({ transferHistory: history });
        console.log('Auto transfer saved to history:', entry);

    } catch (error) {
        console.error('Failed to save auto transfer to history:', error);
    }
}

function parseSelector(selector) {
    // Parse format like "#username", ".email", "*name"
    // Also supports URL targeting: "#<EMAIL>"
    const urlMatch = selector.match(/^([#.*])([^@]+)@(.+)$/);
    if (urlMatch) {
        const [, selectorType, selectorValue, targetUrl] = urlMatch;
        return { selectorType, selectorValue, targetUrl };
    }

    const match = selector.match(/^([#.*])(.+)$/);
    if (!match) {
        throw new Error('Invalid selector format');
    }

    const [, selectorType, selectorValue] = match;
    return { selectorType, selectorValue };
}

async function getSourceValue(tabId, selector) {
    return new Promise(async (resolve, reject) => {
        try {
            // First, ensure content script is injected
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            });
        } catch (error) {
            // Content script might already be injected, continue
        }

        // Get transfer mode from storage
        chrome.storage.local.get(['transferMode'], (result) => {
            const transferMode = result.transferMode || 'default';

            // Small delay to ensure content script is ready
            setTimeout(() => {
                chrome.tabs.sendMessage(tabId, {
                    action: 'getValue',
                    selector: selector,
                    transferMode: transferMode
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error('Could not connect to page. Please refresh and try again.'));
                        return;
                    }

                    if (response && response.success) {
                        resolve(response.value);
                    } else {
                        reject(new Error(response ? response.error : 'Failed to get source value'));
                    }
                });
            }, 100);
        });
    });
}

async function setDestinationValue(tabId, selector, value) {
    return new Promise(async (resolve, reject) => {
        try {
            // First, ensure content scripts are injected
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['ant-datepicker.js', 'content.js']
            });
        } catch (error) {
            // Content scripts might already be injected, continue
        }

        // Small delay to ensure content script is ready
        setTimeout(() => {
            chrome.tabs.sendMessage(tabId, {
                action: 'setValue',
                selector: selector,
                value: value
            }, (response) => {
                if (chrome.runtime.lastError) {
                    reject(new Error('Could not connect to page. Please refresh and try again.'));
                    return;
                }

                if (response && response.success) {
                    resolve();
                } else {
                    reject(new Error(response ? response.error : 'Failed to set destination value'));
                }
            });
        }, 100);
    });
}
