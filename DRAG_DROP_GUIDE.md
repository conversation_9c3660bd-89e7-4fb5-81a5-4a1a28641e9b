# 🎯 Drag & Drop Tab Selection Guide

## 🆕 New Feature: Visual Tab Selection

No more typing URLs! Now you can visually select which tabs to copy from and to using drag & drop.

## 🖱️ How to Use

### 1. **See Available Tabs**
- All your open tabs are listed at the bottom
- Shows tab title and domain
- Automatically refreshes when you open the extension

### 2. **Drag Source Tab**
- **Drag** the tab containing your source element
- **Drop** it in the "📤 Source Tab" container
- This is where the extension will copy FROM

### 3. **Drag Destination Tab**
- **Drag** the tab where you want to paste
- **Drop** it in the "📥 Destination Tab" container  
- This is where the extension will copy TO

### 4. **Transfer with Precision**
- Use your normal selectors: `div-#gtin` → `#gtin`
- Extension will ONLY look in your selected tabs
- No more guessing or searching all tabs!

## 💾 Smart Saving

- **Your selections are saved** automatically
- **Persists between sessions** - no need to re-select
- **Click the × button** to remove a tab selection

## 🔄 Refresh Tabs

- **Click the 🔄 button** to refresh the tab list
- **Useful when** you open new tabs or close tabs
- **Automatically updates** when extension opens

## 🎯 Priority System

The extension now works in this order:

1. **🥇 Selected Tabs** (if you've dragged tabs)
2. **🥈 URL Targeting** (if you use @url syntax)  
3. **🥉 Auto Search** (searches all tabs as before)

## 💡 Perfect for Your Drug System

### Before (confusing):
```
Source: div-#gtin
Destination: #gtin
Result: ❌ Might copy to wrong tab
```

### After (precise):
```
1. Drag "Drug Entry System" to Source Tab
2. Drag "Inventory System" to Destination Tab  
3. Use: div-#gtin → #gtin
Result: ✅ Always copies to correct tab!
```

## 🚀 Benefits

### ✅ **Visual & Intuitive**
- See exactly which tabs you're using
- No typing URLs or domain names
- Clear visual feedback

### ✅ **100% Accurate**
- Never copies to wrong tab again
- Perfect for localhost development
- Works with identical domains

### ✅ **Saves Time**
- Set once, use many times
- No re-typing selectors
- Instant tab identification

### ✅ **Flexible**
- Can still use URL targeting (@url)
- Can still use auto-search mode
- Choose what works best for you

## 🔧 Pro Tips

1. **Set up once** - drag your main tabs and they'll stay selected
2. **Use refresh button** when you open new tabs
3. **Remove selections** (×) to go back to auto-search mode
4. **Mix and match** - select source tab but let destination auto-search

## 🎮 Example Workflow

### Your Drug Management Workflow:
1. **Open extension**
2. **Drag "Drug Entry" tab** to Source Tab container
3. **Drag "Inventory" tab** to Destination Tab container
4. **Enter selectors**: `div-#gtin` → `*gtin`
5. **Click Transfer** - always goes to right place!

### For Development:
1. **Drag localhost:3000** to Source Tab
2. **Drag localhost:8080** to Destination Tab  
3. **Perfect for** copying between dev environments

## 🆘 Troubleshooting

### "No valid tabs found"
- Refresh the tab list (🔄 button)
- Make sure tabs are fully loaded
- Extension can't access chrome:// pages

### Drag not working
- Make sure you're dragging the tab item (not empty space)
- Drop directly on the container area
- Look for blue highlight when dragging over containers

### Tab disappeared from list
- Click refresh (🔄) to reload tab list
- Tab might have been closed or navigated away

This makes your extension much more user-friendly and precise! 🎉
