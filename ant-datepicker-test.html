<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ant DatePicker Test - PageMate Extension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: #fafafa;
        }
        .test-section h3 {
            margin-top: 0;
            color: #262626;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #262626;
        }
        input, select, textarea {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
            transition: border-color 0.3s;
        }
        input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .ant-calendar-picker {
            position: relative;
            display: inline-block;
        }
        .ant-calendar-picker-input {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
            background: white;
            cursor: pointer;
        }
        .ant-calendar-picker-input:hover {
            border-color: #1890ff;
        }
        .ant-calendar-picker-input:focus {
            border-color: #1890ff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        .instructions {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .instructions h4 {
            margin-top: 0;
            color: #0050b3;
        }
        .selector-info {
            background: #fff2e8;
            border: 1px solid #ffbb96;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
        }
        .test-data {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .test-data strong {
            color: #389e0d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗓️ Ant DatePicker Test Page</h1>
        <p>This page simulates Ant Design date picker components to test the PageMate extension's custom date handling.</p>

        <div class="instructions">
            <h4>How to Test:</h4>
            <ol>
                <li>Install the PageMate extension</li>
                <li>Open another tab with a regular input field</li>
                <li>Use PageMate to transfer a date from the source section to the Ant DatePicker below</li>
                <li>Watch the console for detection and automation logs</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>📤 Source Data (Copy From Here)</h3>
            <div class="form-group">
                <label for="sourceDate">Source Date Value:</label>
                <input type="text" id="sourceDate" value="31-03-2028" readonly>
                <div class="selector-info">Selector: #sourceDate</div>
            </div>
            <div class="test-data">
                <strong>Test Date:</strong> 31-03-2028 (DD-MM-YYYY format)
            </div>
        </div>

        <div class="test-section">
            <h3>📥 Ant Design Date Pickers (Paste To Here)</h3>
            
            <div class="form-group">
                <label for="antDatePicker1">Ant DatePicker #1:</label>
                <div class="ant-calendar-picker">
                    <input type="text" 
                           id="antDatePicker1" 
                           class="ant-calendar-picker-input" 
                           placeholder="DD-MM-YYYY" 
                           readonly>
                </div>
                <div class="selector-info">Selector: #antDatePicker1</div>
            </div>

            <div class="form-group">
                <label for="antDatePicker2">Ant DatePicker #2 (with different classes):</label>
                <div class="ant-picker">
                    <input type="text" 
                           id="antDatePicker2" 
                           class="ant-picker-input" 
                           placeholder="Select date" 
                           readonly>
                </div>
                <div class="selector-info">Selector: #antDatePicker2</div>
            </div>

            <div class="form-group">
                <label for="antDatePicker3">Ant DatePicker #3 (nested structure):</label>
                <div class="ant-date-picker">
                    <input type="text" 
                           id="antDatePicker3" 
                           placeholder="YYYY-MM-DD" 
                           readonly>
                </div>
                <div class="selector-info">Selector: #antDatePicker3</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Regular Input Fields (For Comparison)</h3>
            
            <div class="form-group">
                <label for="regularDate">Regular Date Input:</label>
                <input type="text" id="regularDate" placeholder="Enter date manually">
                <div class="selector-info">Selector: #regularDate</div>
            </div>

            <div class="form-group">
                <label for="regularText">Regular Text Input:</label>
                <input type="text" id="regularText" placeholder="Regular text field">
                <div class="selector-info">Selector: #regularText</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🔍 Testing Instructions</h3>
            <ol>
                <li><strong>Test Detection:</strong> Open browser console and transfer to any Ant DatePicker above. Look for "🗓️ Detected Ant Design date picker" messages.</li>
                <li><strong>Test Automation:</strong> The extension should automatically open the popup, type the date, and press Enter.</li>
                <li><strong>Test Fallback:</strong> If automation fails, it should fall back to standard input method.</li>
                <li><strong>Compare Behavior:</strong> Transfer to regular inputs to see the difference in handling.</li>
            </ol>
        </div>
    </div>

    <script>
        // Simulate basic Ant Design date picker behavior
        document.querySelectorAll('.ant-calendar-picker-input, .ant-picker-input').forEach(input => {
            input.addEventListener('click', function() {
                console.log('🗓️ Ant DatePicker clicked:', this.id);
                // Simulate popup opening (in real Ant Design, this would show a calendar)
                this.style.borderColor = '#1890ff';
                this.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
                
                // Simulate popup closing after a delay
                setTimeout(() => {
                    this.style.borderColor = '#d9d9d9';
                    this.style.boxShadow = 'none';
                }, 2000);
            });

            input.addEventListener('focus', function() {
                console.log('🗓️ Ant DatePicker focused:', this.id);
            });

            input.addEventListener('input', function() {
                console.log('🗓️ Ant DatePicker input event:', this.id, 'Value:', this.value);
            });

            input.addEventListener('change', function() {
                console.log('🗓️ Ant DatePicker change event:', this.id, 'Value:', this.value);
            });
        });

        // Log when page is ready
        console.log('🗓️ Ant DatePicker test page loaded');
        console.log('Available Ant DatePickers:', document.querySelectorAll('.ant-calendar-picker-input, .ant-picker-input, .ant-date-picker input').length);
    </script>
</body>
</html>
