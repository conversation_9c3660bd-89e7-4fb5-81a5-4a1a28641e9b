<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageMate</title>
    <link rel="stylesheet" href="popup.css">
</head>

<body>
    <!-- Toast Notification -->
    <div id="toast" class="toast">
        <span id="toastMessage"></span>
    </div>

    <div class="app-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="sidebar-header">
                <div class="app-icon">
                    <img src="icons/icon.png" alt="PageMate" width="50" height="50">
                </div>
            </div>

            <div class="sidebar-nav">
                <button class="nav-btn active" data-page="main" title="Main Dashboard">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                            <polyline points="9,22 9,12 15,12 15,22"/>
                        </svg>
                    </span>
                </button>
                <button class="nav-btn" data-page="history" title="Transfer History">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <polyline points="12,6 12,12 16,14"/>
                        </svg>
                    </span>
                </button>
                <button class="nav-btn" data-page="variables" title="Variables">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                            <circle cx="9" cy="9" r="2"/>
                            <path d="m13 19 2-2 4 4"/>
                        </svg>
                    </span>
                </button>
                <button class="nav-btn" data-page="config" title="Tab Selection">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="3" width="7" height="7" rx="1"/>
                            <rect x="14" y="3" width="7" height="7" rx="1"/>
                            <rect x="14" y="14" width="7" height="7" rx="1"/>
                            <rect x="3" y="14" width="7" height="7" rx="1"/>
                        </svg>
                    </span>
                </button>
                <button class="nav-btn" data-page="settings" title="App Settings">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
                        </svg>
                    </span>
                </button>
            </div>

            <div class="sidebar-footer">
                <button class="nav-btn" data-page="help" title="Help & Info">
                    <span class="nav-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                            <circle cx="12" cy="17" r="0.5"/>
                        </svg>
                    </span>
                </button>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Main Page -->
            <div id="main-page" class="page active">
                <div class="page-header">
                    <h2>PageMate</h2>
                    <p>Your intelligent companion for transferring values across tabs</p>
                </div>

                <div class="connection-status">
                    <div class="status-circle">
                        <span class="status-icon">
                            <img src="icons/gif_icon.gif" alt="PageMate Animation" width="90" height="90">
                        </span>
                    </div>
                    <div class="status-text">Ready to Transfer</div>
                </div>

                <div class="main-stats">
                    <div class="info-card">
                        <div class="info-icon">📊</div>
                        <div class="info-content">
                            <div class="info-number" id="totalTransfers">0</div>
                            <div class="info-label">Total Transfers</div>
                        </div>
                    </div>
                    <div class="info-card">
                        <div class="info-icon">📋</div>
                        <div class="info-content">
                            <div class="info-value" id="lastTransferValue">No transfers yet</div>
                            <div class="info-label">Last Transfer Value</div>
                        </div>
                    </div>
                </div>

                <div class="current-status">
                    <div class="status-item">
                        <span class="status-label">📋 Variables:</span>
                        <span class="status-value" id="currentVariables">Not configured</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">🌐 Source Tab:</span>
                        <span class="status-value" id="currentSourceTab">Not selected</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">🌐 Destination Tab:</span>
                        <span class="status-value" id="currentDestinationTab">Not selected</span>
                    </div>
                </div>

                <div class="main-actions">
                    <button id="mainTransferBtn" class="main-transfer-btn">
                        <span class="transfer-icon">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M7 17l10-10"/>
                                <path d="M17 17h-10v-10"/>
                            </svg>
                        </span>
                        <span class="transfer-text">Transfer Value</span>
                    </button>
                    <button id="autoTransferBtn" class="auto-transfer-btn" title="Enable Auto Transfer">
                        <span class="auto-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M23 4v6h-6"/>
                                <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10"/>
                            </svg>
                        </span>
                        <span class="auto-label">AUTO</span>
                        <span class="auto-status">OFF</span>
                    </button>
                </div>
            </div>

            <!-- History Page -->
            <div id="history-page" class="page">
                <div class="page-header">
                    <h2>📜 Transfer History</h2>
                    <p>View and manage your transfer history</p>
                </div>
                <div class="history-total">
                    <div class="history-stat">
                        <span class="stat-icon">📊</span>
                        <span class="stat-text">Total: <strong id="historyTotal">0</strong></span>
                    </div>
                </div>

                <div id="historyList" class="history-list">
                    <div class="empty-state">
                        <span class="empty-icon">�</span>
                        <p>No transfers yet</p>
                        <small>Your transfer history will appear here</small>
                    </div>
                </div>

                <div class="history-footer">
                    <button id="clearHistoryBtn" class="action-btn clear">
                        <span>🗑️</span>
                        Clear All
                    </button>
                </div>
            </div>

            <!-- Variables Page -->
            <div id="variables-page" class="page">
                <div class="page-header">
                    <h2>📋 Variables</h2>
                    <p>Set up element selectors for data transfer</p>
                </div>

                <!-- Element Selectors Section -->
                <div class="config-section">
                    <div class="variables-header">
                        <h4>🎯 Element Selectors</h4>
                        <button id="addVariableBtn" class="add-variable-btn" title="Add Variable">
                            +
                        </button>
                    </div>

                    <div id="variablesList" class="variables-list">
                        <!-- Dynamic variables will be added here -->
                    </div>
                </div>
            </div>

            <!-- Tab Selection Page -->
            <div id="config-page" class="page">
                <div class="page-header">
                    <h2>🌐 Tab Selection</h2>
                    <p>Select source and destination tabs</p>
                </div>

                <!-- Tab Selection Section -->
                <div class="config-section">
                    <h4>🎯 Source & Destination Tabs</h4>
                    <div class="tab-containers">
                        <div class="tab-container source-container">
                            <div id="sourceTabSlot" class="tab-slot">
                                <div class="slot-placeholder">
                                    <span class="placeholder-icon">📤</span>
                                    <span class="placeholder-text">Drag source tab here</span>
                                </div>
                            </div>
                        </div>

                        <div class="tab-container destination-container">
                            <div id="destinationTabSlot" class="tab-slot">
                                <div class="slot-placeholder">
                                    <span class="placeholder-icon">📥</span>
                                    <span class="placeholder-text">Drag destination tab here</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="available-tabs">
                        <div class="tabs-header">
                            <span>📋 Available Tabs</span>
                            <div class="header-buttons">
                                <button id="refreshSelectedBtn" class="refresh-btn"
                                    title="Update selected tabs">🔄📌</button>
                                <button id="refreshTabsBtn" class="refresh-btn" title="Refresh tab list">🔄</button>
                            </div>
                        </div>
                        <div id="availableTabsList" class="tabs-list"></div>
                    </div>
                </div>
            </div>

            <!-- Settings Page -->
            <div id="settings-page" class="page">
                <div class="page-header">
                    <h2>🔧 App Settings</h2>
                    <p>Customize your transfer experience</p>
                </div>

                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="autoSaveHistory" checked>
                        <span class="checkmark"></span>
                        Save transfer history
                    </label>
                    <small class="setting-description">Keep a record of your transfers</small>
                </div>

                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="showNotifications" checked>
                        <span class="checkmark"></span>
                        Show success notifications
                    </label>
                    <small class="setting-description">Display notifications when transfers complete</small>
                </div>

                <div class="setting-group">
                    <label class="setting-label">
                        <input type="checkbox" id="highlightElements" checked>
                        <span class="checkmark"></span>
                        Highlight transferred elements
                    </label>
                    <small class="setting-description">Briefly highlight elements after transfer</small>
                </div>

                <div class="setting-group">
                    <label for="historyLimit">History limit</label>
                    <select id="historyLimit">
                        <option value="10">10 transfers</option>
                        <option value="25" selected>25 transfers</option>
                        <option value="50">50 transfers</option>
                        <option value="100">100 transfers</option>
                    </select>
                    <small class="setting-description">Maximum number of transfers to keep in history</small>
                </div>
            </div>

            <!-- Help Page -->
            <div id="help-page" class="page">
                <div class="page-header">
                    <h2>❓ Help & Documentation</h2>
                    <p>Learn how to use PageMate</p>
                </div>

                <div class="help-section">
                    <h3>📖 How to Use</h3>

                    <div class="help-content">
                        <h4>🎯 Quick Start:</h4>
                        <ol>
                            <li><strong>Variables Tab:</strong> Configure source and destination selectors</li>
                            <li><strong>Tab Selection:</strong> Choose source and destination tabs (optional)</li>
                            <li><strong>Main Tab:</strong> Click "Transfer Value" to execute</li>
                        </ol>

                        <h4>📝 Source Format (Any Element):</h4>
                        <ul>
                            <li><code>#username</code> - element with ID "username"</li>
                            <li><code>.email</code> - element with class "email"</li>
                            <li><code>*title</code> - element with name "title"</li>
                            <li><code>#<EMAIL></code> - specific tab by URL</li>
                        </ul>

                        <h4>📥 Destination Format (Input Elements Only):</h4>
                        <ul>
                            <li><code>#username</code> - input with ID "username"</li>
                            <li><code>.email</code> - input with class "email"</li>
                            <li><code>*title</code> - input with name "title"</li>
                            <li><code>*<EMAIL></code> - specific tab by URL</li>
                        </ul>

                        <h4>🌐 URL Targeting:</h4>
                        <ul>
                            <li>Add <code>@url</code> to target specific tabs</li>
                            <li>URL can be partial (e.g., <code>@localhost</code>, <code>@example.com</code>)</li>
                            <li>Without @url, searches all tabs automatically</li>
                        </ul>

                        <h4>⚡ Pro Tips:</h4>
                        <ul>
                            <li><strong>Multiple Variables:</strong> Set up multiple source→destination pairs</li>
                            <li><strong>Tab Selection:</strong> Pre-select tabs for faster transfers</li>
                            <li><strong>Auto-Search:</strong> Extension finds elements across all tabs if no specific tab selected</li>
                            <li><strong>Partial Transfers:</strong> Valid variables transfer even if some fail</li>
                            <li><strong>History:</strong> View transfer results in History tab</li>
                        </ul>

                        <h4>🔧 Settings:</h4>
                        <ul>
                            <li><strong>Save History:</strong> Keep transfer records</li>
                            <li><strong>Notifications:</strong> Show success/error messages</li>
                            <li><strong>Highlight:</strong> Flash transferred elements</li>
                            <li><strong>History Limit:</strong> Control storage size</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Hidden transfer button for internal use -->
    <button id="transferBtn" style="display: none;"></button>
    <div id="status" style="display: none;"></div>

    <script src="popup.js"></script>
</body>

</html>