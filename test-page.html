<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PageMate - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .element-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
        input, select, textarea {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .display-element {
            background: #fff3e0;
            padding: 10px;
            border: 1px solid #ffcc02;
            border-radius: 4px;
            margin: 5px;
            display: inline-block;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PageMate - Test Page</h1>
        <p>Use this page to test the PageMate browser extension.</p>

        <div class="instructions">
            <h3>How to Test:</h3>
            <ol>
                <li>Install the PageMate extension</li>
                <li>Click the extension icon in your browser toolbar</li>
                <li>Use the selectors shown below to copy values between elements</li>
                <li>Click "Transfer Value" to see the magic happen!</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>Test Section 1: Basic Elements</h2>

            <div class="element-info">Source: div-#sourceText</div>
            <div id="sourceText" class="display-element">Hello, World! This is source text.</div>

            <div class="element-info">Destination: #targetInput</div>
            <input type="text" id="targetInput" placeholder="Target input field">
        </div>

        <div class="test-section">
            <h2>Test Section 2: Class-based Selection</h2>

            <div class="element-info">Source: span-.email-display</div>
            <span class="email-display display-element"><EMAIL></span>

            <div class="element-info">Destination: .email-input</div>
            <input type="email" class="email-input" placeholder="Email input field">
        </div>

        <div class="test-section">
            <h2>Test Section 3: Name-based Selection</h2>

            <div class="element-info">Source: p-*description</div>
            <p name="description" class="display-element">This is a description paragraph with name attribute.</p>

            <div class="element-info">Destination: *descriptionField</div>
            <textarea name="descriptionField" placeholder="Description textarea" rows="3" cols="50"></textarea>
        </div>

        <div class="test-section">
            <h2>Test Section 4: Input to Input</h2>

            <div class="element-info">Source: input-#sourceValue</div>
            <input type="text" id="sourceValue" value="Pre-filled source value" placeholder="Source input">

            <div class="element-info">Destination: #destinationValue</div>
            <input type="text" id="destinationValue" placeholder="Destination input">
        </div>

        <div class="test-section">
            <h2>Test Section 5: Select Elements</h2>

            <div class="element-info">Source: select-#sourceSelect</div>
            <select id="sourceSelect">
                <option value="">Choose an option</option>
                <option value="option1" selected>Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
            </select>

            <div class="element-info">Destination: #targetSelect</div>
            <select id="targetSelect">
                <option value="">Choose an option</option>
                <option value="option1">Option 1</option>
                <option value="option2">Option 2</option>
                <option value="option3">Option 3</option>
            </select>
        </div>

        <div class="test-section">
            <h2>Test Section 6: Complex Example</h2>

            <div class="element-info">Source: div-#userInfo</div>
            <div id="userInfo" class="display-element">John Doe (<EMAIL>)</div>

            <div class="element-info">Destination: *fullName</div>
            <input type="text" name="fullName" placeholder="Full name field">
        </div>
    </div>

    <script>
        // Add some interactivity to make testing more interesting
        document.getElementById('sourceValue').addEventListener('input', function() {
            console.log('Source value changed:', this.value);
        });

        document.getElementById('destinationValue').addEventListener('input', function() {
            console.log('Destination value changed:', this.value);
        });

        // Log when elements are clicked
        document.querySelectorAll('.display-element').forEach(element => {
            element.addEventListener('click', function() {
                console.log('Clicked element:', this.id || this.className, 'Value:', this.textContent);
            });
        });
    </script>
</body>
</html>
