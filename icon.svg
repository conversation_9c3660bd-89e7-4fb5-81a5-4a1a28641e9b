<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- Transfer arrow -->
  <path d="M30 45 L85 45 L75 35 M85 45 L75 55" stroke="#fff" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Source box -->
  <rect x="20" y="25" width="25" height="20" fill="none" stroke="#fff" stroke-width="2" rx="3"/>
  
  <!-- Destination box -->
  <rect x="83" y="65" width="25" height="20" fill="none" stroke="#fff" stroke-width="2" rx="3"/>
  
  <!-- Transfer arrow (reverse) -->
  <path d="M98 83 L43 83 L53 93 M43 83 L53 73" stroke="#fff" stroke-width="4" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Small dots to represent data -->
  <circle cx="32" cy="35" r="2" fill="#fff"/>
  <circle cx="95" cy="75" r="2" fill="#fff"/>
</svg>
