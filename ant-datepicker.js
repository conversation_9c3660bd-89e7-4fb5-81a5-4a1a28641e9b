// Ant DatePicker Automation Script for PageMate Extension
// This script handles Ant Design date picker components that don't accept direct value setting

(function() {
    'use strict';

    // Main function to detect and handle Ant Design date pickers
    window.PageMateAntDatePicker = {
        
        // Check if an element is an Ant Design date picker
        isAntDatePicker: function(element) {
            if (!element) return false;
            
            // Check for Ant Design date picker classes and attributes
            const antClasses = [
                'ant-calendar-picker',
                'ant-calendar-picker-input',
                'ant-date-picker',
                'ant-picker',
                'ant-picker-input'
            ];
            
            // Check element itself
            for (const className of antClasses) {
                if (element.classList.contains(className)) {
                    console.log('🗓️ Detected Ant Design date picker by class:', className);
                    return true;
                }
            }
            
            // Check parent elements (up to 3 levels)
            let parent = element.parentElement;
            let level = 0;
            while (parent && level < 3) {
                for (const className of antClasses) {
                    if (parent.classList.contains(className)) {
                        console.log('🗓️ Detected Ant Design date picker in parent by class:', className);
                        return true;
                    }
                }
                parent = parent.parentElement;
                level++;
            }
            
            // Check for placeholder patterns that suggest date picker
            const placeholder = element.placeholder || '';
            const datePatterns = ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'Select date'];
            if (datePatterns.some(pattern => placeholder.includes(pattern))) {
                console.log('🗓️ Detected potential date picker by placeholder:', placeholder);
                return true;
            }
            
            return false;
        },
        
        // Set date value using Ant Design popup method
        setDateValue: function(element, dateString) {
            console.log('🗓️ Starting Ant DatePicker automation for value:', dateString);
            
            return new Promise((resolve, reject) => {
                try {
                    // Find the main input element
                    let mainInput = element;
                    
                    // If element is not an input, try to find input within the picker
                    if (element.tagName.toLowerCase() !== 'input') {
                        const inputInPicker = element.querySelector('input');
                        if (inputInPicker) {
                            mainInput = inputInPicker;
                        }
                    }
                    
                    if (!mainInput || mainInput.tagName.toLowerCase() !== 'input') {
                        throw new Error('Could not find input element in date picker');
                    }
                    
                    console.log('🗓️ Found main input element:', mainInput);
                    
                    // Step 1: Click the main input to open popup
                    this.openDatePickerPopup(mainInput)
                        .then(() => {
                            // Step 2: Interact with the popup
                            return this.setDateInPopup(dateString);
                        })
                        .then(() => {
                            // Step 3: Verify the date was set
                            return this.verifyDateWasSet(mainInput, dateString);
                        })
                        .then(() => {
                            console.log('✅ Ant DatePicker automation completed successfully');
                            resolve(true);
                        })
                        .catch((error) => {
                            console.error('❌ Ant DatePicker automation failed:', error);
                            reject(error);
                        });
                        
                } catch (error) {
                    console.error('❌ Ant DatePicker automation error:', error);
                    reject(error);
                }
            });
        },
        
        // Open the date picker popup
        openDatePickerPopup: function(mainInput) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Step 1: Opening date picker popup...');
                
                // Focus and click the input
                mainInput.focus();
                mainInput.click();
                
                // Trigger additional events that might be needed
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                mainInput.dispatchEvent(clickEvent);
                
                // Wait for popup to appear
                setTimeout(() => {
                    resolve();
                }, 300);
            });
        },
        
        // Set date in the popup
        setDateInPopup: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Step 2: Setting date in popup...');
                
                // Look for popup input field
                const popupSelectors = [
                    '.ant-calendar-input',
                    '.ant-calendar-date-input-wrap input',
                    '.ant-calendar-panel input',
                    '.ant-calendar-picker-container input',
                    '.ant-picker-input input',
                    '.ant-picker-panel input'
                ];
                
                let popupInput = null;
                for (const selector of popupSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (const el of elements) {
                        if (el.offsetParent !== null) { // Check if visible
                            popupInput = el;
                            console.log('🗓️ Found popup input with selector:', selector);
                            break;
                        }
                    }
                    if (popupInput) break;
                }
                
                if (!popupInput) {
                    // Try alternative approach - direct calendar click
                    console.log('🗓️ Popup input not found, trying calendar click approach...');
                    this.setDateViaCalendarClick(dateString)
                        .then(resolve)
                        .catch(reject);
                    return;
                }
                
                // Clear and set the value
                popupInput.focus();
                popupInput.value = '';
                popupInput.value = dateString;
                
                // Trigger events
                const inputEvent = new Event('input', { bubbles: true, cancelable: true });
                popupInput.dispatchEvent(inputEvent);
                
                // Press Enter after a delay
                setTimeout(() => {
                    this.pressEnterInPopup(popupInput)
                        .then(resolve)
                        .catch(reject);
                }, 150);
            });
        },
        
        // Press Enter in popup to confirm
        pressEnterInPopup: function(popupInput) {
            return new Promise((resolve) => {
                console.log('🗓️ Step 3: Pressing Enter to confirm...');
                
                // Create Enter key events
                const enterEvents = ['keydown', 'keypress', 'keyup'].map(type => 
                    new KeyboardEvent(type, {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    })
                );
                
                // Dispatch Enter events
                enterEvents.forEach(event => popupInput.dispatchEvent(event));
                
                // Trigger change and blur events
                setTimeout(() => {
                    const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                    const blurEvent = new Event('blur', { bubbles: true, cancelable: true });
                    
                    popupInput.dispatchEvent(changeEvent);
                    popupInput.dispatchEvent(blurEvent);
                    
                    resolve();
                }, 100);
            });
        },
        
        // Alternative method: Click date in calendar
        setDateViaCalendarClick: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Trying calendar click method for:', dateString);
                
                try {
                    const [day, month, year] = dateString.split('-');
                    
                    // Navigate to correct month/year first
                    this.navigateToMonthYear(parseInt(month), parseInt(year))
                        .then(() => {
                            // Click the specific day
                            return this.clickDateInCalendar(day);
                        })
                        .then(resolve)
                        .catch(reject);
                        
                } catch (error) {
                    reject(error);
                }
            });
        },
        
        // Navigate to specific month and year
        navigateToMonthYear: function(targetMonth, targetYear) {
            return new Promise((resolve) => {
                // This is a simplified version - in practice, you might need more complex navigation
                // For now, just resolve and try to click the day
                setTimeout(resolve, 200);
            });
        },
        
        // Click specific day in calendar
        clickDateInCalendar: function(day) {
            return new Promise((resolve) => {
                console.log('🗓️ Looking for day:', day, 'in calendar...');
                
                const dateSelectors = [
                    '.ant-calendar-date',
                    '.ant-picker-cell-inner',
                    '.ant-calendar-cell'
                ];
                
                for (const selector of dateSelectors) {
                    const dateElements = document.querySelectorAll(selector);
                    for (const dateEl of dateElements) {
                        const dayText = dateEl.textContent.trim();
                        if (dayText === day && 
                            !dateEl.closest('.ant-calendar-next-month-btn-day') &&
                            !dateEl.closest('.ant-calendar-prev-month-btn-day')) {
                            
                            console.log('🗓️ Clicking day:', day);
                            dateEl.click();
                            resolve();
                            return;
                        }
                    }
                }
                
                console.log('🗓️ Could not find day in calendar');
                resolve(); // Don't reject, just continue
            });
        },
        
        // Verify the date was set correctly
        verifyDateWasSet: function(mainInput, expectedDate) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    const actualValue = mainInput.value;
                    console.log('🗓️ Verification - Expected:', expectedDate, 'Actual:', actualValue);
                    
                    if (actualValue && (actualValue === expectedDate || actualValue.includes(expectedDate))) {
                        console.log('✅ Date verification successful');
                    } else {
                        console.log('⚠️ Date verification inconclusive, but continuing...');
                    }
                    
                    resolve();
                }, 500);
            });
        }
    };
    
    console.log('🗓️ Ant DatePicker automation script loaded');
    
})();
