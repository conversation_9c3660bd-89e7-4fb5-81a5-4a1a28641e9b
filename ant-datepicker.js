// Ant DatePicker Automation Script for PageMate Extension
// This script handles Ant Design date picker components that don't accept direct value setting

(function() {
    'use strict';

    // Main function to detect and handle Ant Design date pickers
    window.PageMateAntDatePicker = {

        // Check if an element is an Ant Design date picker
        isAntDatePicker: function(element) {
            if (!element) return false;

            console.log('🗓️ Checking element for Ant Design date picker:', element);
            console.log('🗓️ Element ID:', element.id);
            console.log('🗓️ Element classes:', element.className);
            console.log('🗓️ Element placeholder:', element.placeholder);

            // Check for Ant Design date picker classes and attributes
            const antClasses = [
                'ant-calendar-picker',
                'ant-calendar-picker-input',
                'ant-date-picker',
                'ant-picker',
                'ant-picker-input'
            ];

            // Check element itself
            for (const className of antClasses) {
                if (element.classList.contains(className)) {
                    console.log('🗓️ Detected Ant Design date picker by class:', className);
                    return true;
                }
            }

            // Check parent elements (up to 3 levels)
            let parent = element.parentElement;
            let level = 0;
            while (parent && level < 3) {
                console.log(`🗓️ Checking parent level ${level + 1}:`, parent);
                console.log(`🗓️ Parent classes:`, parent.className);

                for (const className of antClasses) {
                    if (parent.classList.contains(className)) {
                        console.log('🗓️ Detected Ant Design date picker in parent by class:', className);
                        return true;
                    }
                }
                parent = parent.parentElement;
                level++;
            }

            // Check for placeholder patterns that suggest date picker
            const placeholder = element.placeholder || '';
            const datePatterns = ['DD-MM-YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD', 'Select date', 'datePicker'];
            if (datePatterns.some(pattern => placeholder.includes(pattern))) {
                console.log('🗓️ Detected potential date picker by placeholder:', placeholder);
                return true;
            }

            // Check for ID patterns that suggest date picker
            const elementId = element.id || '';
            const idPatterns = ['datePicker', 'date-picker', 'calendar', 'expire'];
            if (idPatterns.some(pattern => elementId.toLowerCase().includes(pattern.toLowerCase()))) {
                console.log('🗓️ Detected potential date picker by ID pattern:', elementId);
                return true;
            }

            console.log('🗓️ No Ant Design date picker detected');
            return false;
        },

        // Set date value using Ant Design popup method
        setDateValue: function(element, dateString) {
            console.log('🗓️ Starting Ant DatePicker automation for value:', dateString);

            return new Promise((resolve, reject) => {
                try {
                    // Find the main input element
                    let mainInput = element;

                    // If element is not an input, try to find input within the picker
                    if (element.tagName.toLowerCase() !== 'input') {
                        const inputInPicker = element.querySelector('input');
                        if (inputInPicker) {
                            mainInput = inputInPicker;
                        }
                    }

                    if (!mainInput || mainInput.tagName.toLowerCase() !== 'input') {
                        throw new Error('Could not find input element in date picker');
                    }

                    console.log('🗓️ Found main input element:', mainInput);

                    // Step 1: Click the main input to open popup
                    this.openDatePickerPopup(mainInput)
                        .then(() => {
                            // Step 2: Interact with the popup
                            return this.setDateInPopup(dateString);
                        })
                        .then(() => {
                            // Step 3: Verify the date was set
                            return this.verifyDateWasSet(mainInput, dateString);
                        })
                        .then(() => {
                            console.log('✅ Ant DatePicker automation completed successfully');
                            resolve(true);
                        })
                        .catch((error) => {
                            console.error('❌ Ant DatePicker automation failed:', error);
                            reject(error);
                        });

                } catch (error) {
                    console.error('❌ Ant DatePicker automation error:', error);
                    reject(error);
                }
            });
        },

        // Open the date picker popup
        openDatePickerPopup: function(mainInput) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Step 1: Opening date picker popup...');

                // Focus and click the input
                mainInput.focus();
                mainInput.click();

                // Trigger additional events that might be needed
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                mainInput.dispatchEvent(clickEvent);

                // Wait for popup to appear
                setTimeout(() => {
                    resolve();
                }, 300);
            });
        },

        // Set date in the popup
        setDateInPopup: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Step 2: Setting date in popup...');

                // Look for popup input field
                const popupSelectors = [
                    '.ant-calendar-input',
                    '.ant-calendar-date-input-wrap input',
                    '.ant-calendar-panel input',
                    '.ant-calendar-picker-container input',
                    '.ant-picker-input input',
                    '.ant-picker-panel input'
                ];

                let popupInput = null;
                for (const selector of popupSelectors) {
                    console.log('🗓️ Trying popup selector:', selector);
                    const elements = document.querySelectorAll(selector);
                    console.log('🗓️ Found elements:', elements.length);

                    for (const el of elements) {
                        console.log('🗓️ Checking element visibility:', el, 'offsetParent:', el.offsetParent);
                        if (el.offsetParent !== null) { // Check if visible
                            popupInput = el;
                            console.log('🗓️ Found popup input with selector:', selector);
                            break;
                        }
                    }
                    if (popupInput) break;
                }

                if (!popupInput) {
                    // Try alternative approach - direct value setting on main input
                    console.log('🗓️ Popup input not found, trying direct value setting...');
                    this.setDateDirectly(dateString)
                        .then(resolve)
                        .catch(() => {
                            // If direct setting fails, try calendar click
                            console.log('🗓️ Direct setting failed, trying calendar click approach...');
                            this.setDateViaCalendarClick(dateString)
                                .then(resolve)
                                .catch(reject);
                        });
                    return;
                }

                // Clear and set the value
                console.log('🗓️ Setting value in popup input:', popupInput);
                popupInput.focus();

                // Clear existing value
                popupInput.value = '';
                popupInput.select();

                // Set new value
                popupInput.value = dateString;

                // Trigger multiple events to ensure the framework detects the change
                const events = [
                    new Event('input', { bubbles: true, cancelable: true }),
                    new Event('change', { bubbles: true, cancelable: true }),
                    new Event('blur', { bubbles: true, cancelable: true })
                ];

                events.forEach(event => {
                    console.log('🗓️ Dispatching event:', event.type);
                    popupInput.dispatchEvent(event);
                });

                // Press Enter after a delay
                setTimeout(() => {
                    this.pressEnterInPopup(popupInput)
                        .then(resolve)
                        .catch(reject);
                }, 200);
            });
        },

        // Press Enter in popup to confirm
        pressEnterInPopup: function(popupInput) {
            return new Promise((resolve) => {
                console.log('🗓️ Step 3: Pressing Enter to confirm...');

                // Create Enter key events
                const enterEvents = ['keydown', 'keypress', 'keyup'].map(type =>
                    new KeyboardEvent(type, {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    })
                );

                // Dispatch Enter events
                enterEvents.forEach(event => popupInput.dispatchEvent(event));

                // Trigger change and blur events
                setTimeout(() => {
                    const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                    const blurEvent = new Event('blur', { bubbles: true, cancelable: true });

                    popupInput.dispatchEvent(changeEvent);
                    popupInput.dispatchEvent(blurEvent);

                    resolve();
                }, 100);
            });
        },

        // Alternative method: Click date in calendar
        setDateViaCalendarClick: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Trying calendar click method for:', dateString);

                try {
                    const [day, month, year] = dateString.split('-');

                    // Navigate to correct month/year first
                    this.navigateToMonthYear(parseInt(month), parseInt(year))
                        .then(() => {
                            // Click the specific day
                            return this.clickDateInCalendar(day);
                        })
                        .then(resolve)
                        .catch(reject);

                } catch (error) {
                    reject(error);
                }
            });
        },

        // Navigate to specific month and year
        navigateToMonthYear: function(targetMonth, targetYear) {
            return new Promise((resolve) => {
                // This is a simplified version - in practice, you might need more complex navigation
                // For now, just resolve and try to click the day
                setTimeout(resolve, 200);
            });
        },

        // Click specific day in calendar
        clickDateInCalendar: function(day) {
            return new Promise((resolve) => {
                console.log('🗓️ Looking for day:', day, 'in calendar...');

                const dateSelectors = [
                    '.ant-calendar-date',
                    '.ant-picker-cell-inner',
                    '.ant-calendar-cell'
                ];

                for (const selector of dateSelectors) {
                    const dateElements = document.querySelectorAll(selector);
                    for (const dateEl of dateElements) {
                        const dayText = dateEl.textContent.trim();
                        if (dayText === day &&
                            !dateEl.closest('.ant-calendar-next-month-btn-day') &&
                            !dateEl.closest('.ant-calendar-prev-month-btn-day')) {

                            console.log('🗓️ Clicking day:', day);
                            dateEl.click();
                            resolve();
                            return;
                        }
                    }
                }

                console.log('🗓️ Could not find day in calendar');
                resolve(); // Don't reject, just continue
            });
        },

        // Try setting date directly on the main input (fallback method)
        setDateDirectly: function(dateString) {
            return new Promise((resolve, reject) => {
                console.log('🗓️ Attempting direct date setting...');

                // Find the main input element again
                const mainInput = document.querySelector('input[placeholder*="DD-MM-YYYY"], input[placeholder*="Select date"], input[id*="datePicker"], input[id*="date-picker"], input[id*="expire"]');

                if (!mainInput) {
                    reject(new Error('Main input not found for direct setting'));
                    return;
                }

                console.log('🗓️ Setting date directly on main input:', mainInput);

                // Focus and clear
                mainInput.focus();
                mainInput.value = '';
                mainInput.select();

                // Set the value
                mainInput.value = dateString;

                // Trigger comprehensive events
                const events = [
                    new Event('focus', { bubbles: true }),
                    new Event('input', { bubbles: true, cancelable: true }),
                    new Event('change', { bubbles: true, cancelable: true }),
                    new Event('blur', { bubbles: true })
                ];

                events.forEach(event => {
                    console.log('🗓️ Dispatching direct event:', event.type);
                    mainInput.dispatchEvent(event);
                });

                // Verify after a delay
                setTimeout(() => {
                    if (mainInput.value === dateString) {
                        console.log('✅ Direct date setting successful');
                        resolve();
                    } else {
                        console.log('❌ Direct date setting failed');
                        reject(new Error('Direct setting verification failed'));
                    }
                }, 300);
            });
        },

        // Verify the date was set correctly
        verifyDateWasSet: function(mainInput, expectedDate) {
            return new Promise((resolve) => {
                setTimeout(() => {
                    const actualValue = mainInput.value;
                    console.log('🗓️ Verification - Expected:', expectedDate, 'Actual:', actualValue);

                    if (actualValue && (actualValue === expectedDate || actualValue.includes(expectedDate))) {
                        console.log('✅ Date verification successful');
                    } else {
                        console.log('⚠️ Date verification inconclusive, but continuing...');
                    }

                    resolve();
                }, 500);
            });
        }
    };

    console.log('🗓️ Ant DatePicker automation script loaded');

})();
